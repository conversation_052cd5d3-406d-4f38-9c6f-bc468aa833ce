# Field Access Extension for Dynamic Values

## Goal
Extend dynamic values system to support field access expressions like `{game/195444/rewardRollResult.isLoading}` instead of just `{game/195444/rewardRollResult}`.

## Current State
- Dynamic values support basic path expressions: `{category/widgetId/property}`
- Expression parser validates paths with minimum 3 parts
- Values are resolved as complete objects and rendered using JSON.stringify for objects
- RewardRoll objects have nested structure: `{ id, roundId, gameWidgetId, timestamp, result: { hasWon, reward } }`

## Required Changes

### 1. Update Expression Parser
- Modify regex in `parseExpression()` to capture field access: `{path.field.subfield}`
- Split path into base path and field access parts
- Update validation to allow field access syntax
- Store field access path separately in ParsedExpression

### 2. Update Value Resolution
- Modify `renderExpression()` and `renderExpressionWithVariables()` to handle field access
- Implement safe property access with fallback for undefined/null values
- Support nested field access (e.g., `result.reward.name`)

### 3. Update Types
- Extend `ParsedExpression` interface to include `fieldPath?: string`
- Update validation logic to handle field access paths

### 4. Add Field Access Utility
- Create utility function for safe nested property access
- Handle array indices if needed in future
- Provide meaningful fallbacks for missing properties

## Implementation Steps

### Step 1: Update ParsedExpression Interface
- Add `fieldPath?: string` to store field access part
- Update expression parser types

### Step 2: Modify Expression Parser
- Update regex to capture field access: `/\{([^.}]+)(?:\.([^}]+))?\}/g`
- Split captured groups into base path and field path
- Update validation logic

### Step 3: Implement Field Access Resolution
- Create `getNestedValue()` utility function
- Update render functions to use field access when present
- Handle edge cases (null/undefined values)

### Step 4: Test with RewardRoll Examples
- Test `{game/195444/rewardRollResult.isLoading}`
- Test `{game/195444/rewardRollResult.result.hasWon}`
- Test `{game/195444/rewardRollResult.result.reward.name}`

## Files to Modify
- `packages/shared/lib/dynamic-values/expression-parser.ts`
- `packages/shared/lib/dynamic-values/types.ts`

## Testing Strategy
- Unit tests for expression parsing with field access
- Integration tests with actual RewardRoll objects
- Verify backward compatibility with existing expressions
