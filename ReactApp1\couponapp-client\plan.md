# Field Access Expression Support Plan

## Goal
Extend dynamic values expressions to support field access (dot notation) for accessing nested object properties.

**Current:** `{game/195444/rewardRollResult}`
**Target:** `{game/195444/rewardRollResult.isLoading}`, `{game/195444/rewardRollResult.result.hasWon}`

## Current System Analysis
- Expression parser handles `{path}` syntax in `expression-parser.ts`
- `valueGetter(path)` returns complete objects from dynamic values provider
- Objects like `<PERSON>ward<PERSON>oll` have nested structure:
  ```typescript
  RewardRoll {
    id: string
    roundId: string
    gameWidgetId: string
    timestamp: number
    result?: {
      hasWon: boolean
      reward?: RewardDefinition
    }
  }
  ```
- `RollResult` from `useRoundReward` adds `isLoading` and `error` fields

## Implementation Plan

### 1. Update Expression Parser
**File:** `packages/shared/lib/dynamic-values/expression-parser.ts`

- Modify `parseExpression()` to detect field access syntax
- Split path into base path and field access chain
- Update `ParsedExpression` interface to include field access info
- Validate field access syntax during parsing

### 2. Update Value Resolution
**File:** `packages/shared/lib/dynamic-values/expression-parser.ts`

- Modify `renderExpression()` and `renderExpressionWithVariables()`
- After getting base object from `valueGetter()`, traverse field access chain
- Handle nested property access safely (null/undefined checks)
- Support array access if needed

### 3. Update Type Definitions
**File:** `packages/shared/lib/dynamic-values/types.ts`

- Add field access properties to `ParsedExpression`
- Update documentation for supported field access patterns

## Technical Details

### Expression Syntax
- Base path: `game/195444/rewardRollResult`
- Field access: `game/195444/rewardRollResult.isLoading`
- Nested access: `game/195444/rewardRollResult.result.hasWon`
- Deep nesting: `game/195444/rewardRollResult.result.reward.name`

### Field Access Implementation
```typescript
function getNestedValue(obj: any, fieldPath: string): any {
  return fieldPath.split('.').reduce((current, field) => 
    current?.[field], obj
  )
}
```

### Parsing Changes
- Regex update to capture field access: `/\{([^}]+)\}/g`
- Split captured content on first dot to separate base path from fields
- Store field access chain in `ParsedExpression`

## Files to Modify
1. `packages/shared/lib/dynamic-values/expression-parser.ts` - Core parsing and rendering
2. `packages/shared/lib/dynamic-values/types.ts` - Type definitions

## Testing Strategy
- Unit tests for parsing expressions with field access
- Test nested property access with RewardRoll objects
- Verify backward compatibility with existing expressions
- Test error handling for invalid field access

## Examples to Test
- `{game/195444/rewardRollResult.isLoading}` → boolean
- `{game/195444/rewardRollResult.result.hasWon}` → boolean  
- `{game/195444/rewardRollResult.result.reward.name}` → string
- `{game/195444/rewardRollResult.nonExistent}` → fallback value
